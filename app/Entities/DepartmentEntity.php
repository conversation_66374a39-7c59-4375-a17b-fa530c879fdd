<?php

namespace App\Entities;

class DepartmentEntity extends BaseEntity
{
    public static string $table = 'departments';

    public static array $fields = [
        'id',
        'created_at',
        'updated_at',
        'deleted_at',
        'cabinet_id',
        'name',
        'sorting',
        'is_default',
        'sales_channel_id',
        'is_common',
        'sort',
    ];

    public function sales_channels(): RelationBuilder
    {
        return $this->hasOne(SalesChannelEntity::class, 'sales_channel_id', 'id');
    }
}
