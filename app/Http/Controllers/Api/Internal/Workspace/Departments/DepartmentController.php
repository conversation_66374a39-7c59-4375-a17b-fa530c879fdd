<?php

namespace App\Http\Controllers\Api\Internal\Workspace\Departments;

use App\Contracts\Policies\BaseResourcePolicyContract;
use App\Contracts\Policies\Cabinet\DepartmentPolicyContract;
use App\Contracts\Services\Internal\DepartmentsServiceContract;
use App\Http\Controllers\Controller;
use App\Http\Requests\Api\Internal\Departaments\DepartamentIndexRequest;
use App\Http\Requests\Api\Internal\Departaments\DepartamentStoreRequest;
use App\Http\Requests\Api\Internal\Departaments\DepartamentUpdateRequest;
use App\Http\Resources\Workspace\Departments\DepartmentIndexCollection;
use App\Traits\ApiPolicy;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class DepartmentController extends Controller
{
    use ApiPolicy;

    public function __construct(
        private readonly DepartmentsServiceContract $service,
        private readonly DepartmentPolicyContract $policy
    ) {
    }

    /**
     * @response DepartmentIndexCollection<DepartmentIndexResource>
     */
    public function index(DepartamentIndexRequest $request): JsonResponse
    {
        return $this->executeAction(function () use ($request) {
            $data = $request->toDTO();
            $this->policy->index($data->id);

            $data = $this->service->index($data);
            $collection = new DepartmentIndexCollection($data['data']);
            return $this->successResponse($collection->additional(['meta' => $data['meta']]));
        });
    }

    public function store(DepartamentStoreRequest $request): JsonResponse
    {
        return $this->executeAction(function () use ($request) {
            $data = $request->toDto();

            $this->authorizeCreate($request, $data);

            $id = $this->service->create($data);
            return $this->createdResponse($id);
        });
    }

    /**
     * @response DepartmentShowResource
     */
    public function show(Request $request, string $id): JsonResponse
    {
        return $this->executeAction(function () use ($request, $id) {
            $this->authorizeView($request, $id);

            $data = $this->service->show($id);
            return $this->successResponse(DepartmentShowResource::make($data));
        });
    }

    public function update(DepartamentUpdateRequest $request, string $id): JsonResponse
    {
        return $this->executeAction(function () use ($request) {
            $data = $request->toDto();

            $this->authorizeUpdate($request, $data);

            $this->service->update($data);
            return $this->noContentResponse();
        });
    }

    public function destroy(Request $request, string $id): JsonResponse
    {
        return $this->executeAction(function () use ($request, $id) {
            $this->authorizeDelete($request, $id);

            $this->service->delete($id);
            return $this->noContentResponse();
        });
    }

    protected function getPolicy(): BaseResourcePolicyContract
    {
        return $this->policy;
    }
}
