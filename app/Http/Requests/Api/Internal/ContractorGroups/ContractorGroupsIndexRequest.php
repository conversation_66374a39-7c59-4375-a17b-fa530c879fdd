<?php

namespace App\Http\Requests\Api\Internal\ContractorGroups;

use App\DTO\IndexRequestDTO;
use App\Entities\ContractorGroupsEntity;
use App\Rules\AllowedFieldsRule;
use App\Rules\ValidSortFieldRule;
use Illuminate\Foundation\Http\FormRequest;

class ContractorGroupsIndexRequest extends FormRequest
{
    public function __construct(
        private readonly ContractorGroupsEntity $entity
    ) {
        parent::__construct();
    }
    /**
     * Determine if the user is authorized to make this request.
     */
    public function rules(): array
    {
        return [
            'cabinet_id' => 'required|UUID',

            'filters' => 'nullable|array',

            'filters.name.value' => 'string',

            'filters.search.value' => 'string',

            'fields' => 'nullable|array',
            'fields.*' => ['nullable', 'string', new AllowedFieldsRule($this->entity)],
            'sortField' => ['nullable', 'string', new ValidSortFieldRule($this->entity, $this)],
            'sortDirection' => 'nullable|string|in:asc,desc',
            'page' => 'nullable|integer|min:1',
            'per_page' => 'nullable|integer|min:1|max:100'
        ];
    }

    public function toDTO(): IndexRequestDTO
    {
        $data = $this->validated();
        return IndexRequestDTO::fromArray(
            array_merge(
                ['id' => $data['cabinet_id']],
                $data
            )
        );
    }
}
