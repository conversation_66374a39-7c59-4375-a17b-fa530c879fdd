<?php

namespace App\Http\Resources\User;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class UserProfileResource extends JsonResource
{
    public function toArray(Request $request): array
    {
        return [
            /** @var int $id Уникальный идентификатор пользователя */
            'id' => (int) $this->id,
            /** @var string $created_at Дата и время создания */
            'created_at' => $this->created_at,
            /** @var string $updated_at Дата и время последнего обновления */
            'updated_at' => $this->updated_at,
            /** @var string|null $deleted_at Дата и время удаления */
            'deleted_at' => $this->deleted_at,
            /** @var string|null $lastname Фамилия */
            'lastname' => $this->lastname,
            /** @var string $firstname Имя */
            'firstname' => (string) $this->firstname,
            /** @var string|null $patronymic Отчество */
            'patronymic' => $this->patronymic,
            /** @var string|null $tel Телефон */
            'tel' => $this->tel,
            /** @var string $email Email */
            'email' => (string) $this->email,
            /** @var string|null $email_verified_at Дата верификации email */
            'email_verified_at' => $this->email_verified_at,
            /** @var string|null $current_cabinet_id Идентификатор текущего кабинета */
            'current_cabinet_id' => $this->current_cabinet_id,
            /** @var array{id: int, user_id: int, inn: string|null, language: string, printing_documents: bool, additional_fields: int, start_screen: string|null, update_reports_automatically: bool, signature_sent_emails: string|null, image: string|null}|null $userSettings Настройки пользователя */
            'userSettings' => $this->when($this->userSettings, function () {
                return [
                    'id' => (int) $this->userSettings->id,
                    'user_id' => (int) $this->userSettings->user_id,
                    'inn' => $this->userSettings->inn,
                    'language' => (string) $this->userSettings->language,
                    'printing_documents' => (bool) $this->userSettings->printing_documents,
                    'additional_fields' => (int) $this->userSettings->additional_fields,
                    'start_screen' => $this->userSettings->start_screen,
                    'update_reports_automatically' => (bool) $this->userSettings->update_reports_automatically,
                    'signature_sent_emails' => $this->userSettings->signature_sent_emails,
                    'image' => $this->userSettings->image,
                    'created_at' => $this->userSettings->created_at,
                    'updated_at' => $this->userSettings->updated_at,
                    'discount' => $this->userSettings->discount,
                ];
            }),
        ];
    }
}
