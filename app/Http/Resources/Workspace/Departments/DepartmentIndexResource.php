<?php

namespace App\Http\Resources\Workspace\Departments;

use App\Http\Resources\Relations\SalesChannelRelationResource;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class DepartmentIndexResource extends JsonResource
{
    public function toArray(Request $request): array
    {
        return [
            /** @var string $id Уникальный идентификатор отдела */
            'id' => $this->id,
            /** @var string $created_at Дата и время создания */
            'created_at' => $this->when(
                property_exists($this->resource, 'created_at'),
                function () {
                    return $this->resource->created_at;
                }
            ),
            /** @var string $updated_at Дата и время последнего обновления */
            'updated_at' => $this->when(
                property_exists($this->resource, 'updated_at'),
                function () {
                    return $this->resource->updated_at;
                }
            ),
            /** @var string|null $deleted_at Дата и время удаления */
            'deleted_at' => $this->when(
                property_exists($this->resource, 'deleted_at'),
                function () {
                    return $this->resource->deleted_at;
                }
            ),
            /** @var string $cabinet_id Идентификатор кабинета */
            'cabinet_id' => $this->when(
                property_exists($this->resource, 'cabinet_id'),
                function () {
                    return $this->resource->cabinet_id;
                }
            ),
            /** @var string $name Название отдела */
            'name' => $this->when(
                property_exists($this->resource, 'name'),
                function () {
                    return (string) $this->resource->name;
                }
            ),
            /** @var int $sorting Порядок сортировки */
            'sorting' => $this->when(
                property_exists($this->resource, 'sorting'),
                function () {
                    return (int) $this->resource->sorting;
                }
            ),
            /** @var bool $is_default Отдел по умолчанию */
            'is_default' => $this->when(
                property_exists($this->resource, 'is_default'),
                function () {
                    return (bool) $this->resource->is_default;
                }
            ),
            /** @var string|null $sales_channel_id Идентификатор канала продаж */
            'sales_channel_id' => $this->when(
                property_exists($this->resource, 'sales_channel_id'),
                function () {
                    return $this->resource->sales_channel_id;
                }
            ),
            /** @var bool $is_common Общий отдел */
            'is_common' => $this->when(
                property_exists($this->resource, 'is_common'),
                function () {
                    return (bool) $this->resource->is_common;
                }
            ),
            /** @var int $sort Сортировка */
            'sort' => $this->when(
                property_exists($this->resource, 'sort'),
                function () {
                    return (int) $this->resource->sort;
                }
            ),
            'sales_channels' => $this->when(property_exists($this->resource, 'sales_channels'), function () {
                return new SalesChannelRelationResource($this->resource->sales_channels ?? []);
            }),
        ];
    }
}
