<?php

namespace Tests\Feature;

use App\Models\User;
use App\Models\Cabinet;
use App\Models\Employee;
use App\Models\Department;
use App\Models\CabinetSettings;
use App\Models\CabinetEmployee;
use App\Models\ContractorGroups;
use Tests\TestCase;
use Illuminate\Foundation\Testing\WithFaker;
use Illuminate\Foundation\Testing\RefreshDatabase;

class ContractorGroupsTest extends TestCase
{
    use RefreshDatabase;
    use WithFaker;

    protected function setUp(): void
    {
        parent::setUp();

        $user = User::factory()->create();
        $this->actingAs($user, 'sanctum');

        $this->employee = Employee::factory()->create([
            'user_id' => $user->id,
        ]);

        $this->cabinet = Cabinet::factory()->create([
            'user_id' => $user->id,
        ]);

        CabinetSettings::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        $this->otherCabinet = Cabinet::factory()->create();

        CabinetEmployee::factory()->create([
            'employee_id' => $this->employee->id,
            'cabinet_id' => $this->cabinet->id
        ]);

        $this->department = Department::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);
    }

    public function test_can_get_contractor_groups_list(): void
    {
        // Arrange
        ContractorGroups::factory()->count(3)->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        // Создаем группу для другого кабинета
        ContractorGroups::factory()->create([
            'cabinet_id' => $this->otherCabinet->id
        ]);

        // Act
        $response = $this->getJson('/api/internal/contractor_groups?' . http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'page' => 1,
            'per_page' => 15
        ]));

        // Assert
        $response->assertStatus(200)
            ->assertJsonStructure([
                'data' => [
                    '*' => [
                        'id',
                        'created_at',
                        'updated_at',
                        'cabinet_id',
                        'name',
                    ]
                ],
                'meta' => [
                    'current_page',
                    'per_page',
                    'last_page',
                    'total'
                ]
            ]);

        $response->assertJsonCount(3, 'data');
    }

    public function test_cannot_access_other_cabinet_contractor_groups(): void
    {
        // Act
        $response = $this->getJson('/api/internal/contractor_groups?' . http_build_query([
            'cabinet_id' => $this->otherCabinet->id
        ]));

        // Assert
        $response->assertStatus(403);
    }

    public function test_index_validation_errors(): void
    {
        // Act
        $response = $this->getJson('/api/internal/contractor_groups?' . http_build_query([
            'cabinet_id' => 'invalid-uuid',
            'page' => 0,
            'per_page' => 101,
            'sortDirection' => 'invalid',
        ]));

        // Assert
        $response->assertStatus(422)
            ->assertJsonValidationErrors([
                'cabinet_id',
                'page',
                'per_page',
                'sortDirection',
            ]);
    }

    public function test_index_filter_search(): void
    {
        // Arrange
        $group = ContractorGroups::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'name' => 'Test Group'
        ]);

        ContractorGroups::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'name' => 'Other Group'
        ]);

        // Создаем группу с таким же названием в другом кабинете
        ContractorGroups::factory()->create([
            'cabinet_id' => $this->otherCabinet->id,
            'name' => 'Test Group'
        ]);

        // Act
        $response = $this->getJson('/api/internal/contractor_groups?' . http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'filters' => [
                'search' => [
                    'value' => 'Test'
                ]
            ]
        ]));

        // Assert
        $response->assertStatus(200)
            ->assertJsonCount(1, 'data')
            ->assertJsonPath('data.0.name', 'Test Group')
            ->assertJsonPath('data.0.cabinet_id', $this->cabinet->id);
    }

    public function test_can_create_contractor_group(): void
    {
        // Arrange
        $data = [
            'cabinet_id' => $this->cabinet->id,
            'name' => 'New Test Group'
        ];

        // Act
        $response = $this->postJson('/api/internal/contractor_groups', $data);

        // Assert
        $response->assertStatus(201)
            ->assertJsonStructure(['id']);

        $this->assertDatabaseHas('contractor_groups', [
            'cabinet_id' => $this->cabinet->id,
            'name' => 'New Test Group'
        ]);
    }

    public function test_create_validation_errors(): void
    {
        // Act
        $response = $this->postJson('/api/internal/contractor_groups', [
            'cabinet_id' => 'invalid-uuid',
            'name' => ''
        ]);

        // Assert
        $response->assertStatus(422)
            ->assertJsonValidationErrors([
                'cabinet_id',
                'name'
            ]);
    }

    public function test_cannot_create_contractor_group_for_other_cabinet(): void
    {
        // Arrange
        $data = [
            'cabinet_id' => $this->otherCabinet->id,
            'name' => 'Test Group'
        ];

        // Act
        $response = $this->postJson('/api/internal/contractor_groups', $data);

        // Assert
        $response->assertStatus(403);
    }

    public function test_can_show_contractor_group(): void
    {
        // Arrange
        $group = ContractorGroups::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'name' => 'Test Group'
        ]);

        // Act
        $response = $this->getJson("/api/internal/contractor_groups/{$group->id}");

        // Assert
        $response->assertStatus(200)
            ->assertJsonStructure([
                'id',
                'created_at',
                'updated_at',
                'cabinet_id',
                'name'
            ])
            ->assertJsonPath('id', $group->id)
            ->assertJsonPath('name', 'Test Group')
            ->assertJsonPath('cabinet_id', $this->cabinet->id);
    }

    public function test_cannot_show_contractor_group_from_other_cabinet(): void
    {
        // Arrange
        $group = ContractorGroups::factory()->create([
            'cabinet_id' => $this->otherCabinet->id
        ]);

        // Act
        $response = $this->getJson("/api/internal/contractor_groups/{$group->id}");

        // Assert
        $response->assertStatus(403);
    }

    public function test_can_update_contractor_group(): void
    {
        // Arrange
        $group = ContractorGroups::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'name' => 'Original Name'
        ]);

        $data = [
            'name' => 'Updated Name'
        ];

        // Act
        $response = $this->putJson("/api/internal/contractor_groups/{$group->id}", $data);

        // Assert
        $response->assertStatus(204);

        $this->assertDatabaseHas('contractor_groups', [
            'id' => $group->id,
            'name' => 'Updated Name'
        ]);
    }

    public function test_update_validation_errors(): void
    {
        // Arrange
        $group = ContractorGroups::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        // Act
        $response = $this->putJson("/api/internal/contractor_groups/{$group->id}", [
            'name' => ''
        ]);

        // Assert
        $response->assertStatus(422)
            ->assertJsonValidationErrors(['name']);
    }

    public function test_cannot_update_contractor_group_from_other_cabinet(): void
    {
        // Arrange
        $group = ContractorGroups::factory()->create([
            'cabinet_id' => $this->otherCabinet->id
        ]);

        // Act
        $response = $this->putJson("/api/internal/contractor_groups/{$group->id}", [
            'name' => 'Updated Name'
        ]);

        // Assert
        $response->assertStatus(403);
    }

    public function test_can_delete_contractor_group(): void
    {
        // Arrange
        $group = ContractorGroups::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        // Act
        $response = $this->deleteJson("/api/internal/contractor_groups/{$group->id}");

        // Assert
        $response->assertStatus(204);

        $this->assertDatabaseMissing('contractor_groups', [
            'id' => $group->id
        ]);
    }

    public function test_cannot_delete_contractor_group_from_other_cabinet(): void
    {
        // Arrange
        $group = ContractorGroups::factory()->create([
            'cabinet_id' => $this->otherCabinet->id
        ]);

        // Act
        $response = $this->deleteJson("/api/internal/contractor_groups/{$group->id}");

        // Assert
        $response->assertStatus(403);
    }

    public function test_can_bulk_delete_contractor_groups(): void
    {
        // Arrange
        $groups = ContractorGroups::factory()->count(3)->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        $groupIds = $groups->pluck('id')->toArray();

        $data = [
            'cabinet_id' => $this->cabinet->id,
            'ids' => $groupIds
        ];

        // Act
        $response = $this->deleteJson('/api/internal/contractor_groups/bulk-delete', $data);

        // Assert
        $response->assertStatus(204);

        foreach ($groupIds as $groupId) {
            $this->assertDatabaseMissing('contractor_groups', [
                'id' => $groupId
            ]);
        }
    }

    public function test_bulk_delete_validation_errors(): void
    {
        // Act
        $response = $this->deleteJson('/api/internal/contractor_groups/bulk-delete', [
            'cabinet_id' => 'invalid-uuid',
            'ids' => ['invalid-uuid', '']
        ]);

        // Assert
        $response->assertStatus(422)
            ->assertJsonValidationErrors([
                'cabinet_id',
                'ids.0',
                'ids.1'
            ]);
    }

    public function test_cannot_bulk_delete_contractor_groups_from_other_cabinet(): void
    {
        // Arrange
        $groups = ContractorGroups::factory()->count(2)->create([
            'cabinet_id' => $this->otherCabinet->id
        ]);

        $data = [
            'cabinet_id' => $this->otherCabinet->id,
            'ids' => $groups->pluck('id')->toArray()
        ];

        // Act
        $response = $this->deleteJson('/api/internal/contractor_groups/bulk-delete', $data);

        // Assert
        $response->assertStatus(403);
    }

    public function test_index_with_sorting(): void
    {
        // Arrange
        ContractorGroups::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'name' => 'A Group'
        ]);

        ContractorGroups::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'name' => 'Z Group'
        ]);

        // Act - сортировка по убыванию
        $response = $this->getJson('/api/internal/contractor_groups?' . http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'sortField' => 'name',
            'sortDirection' => 'desc'
        ]));

        // Assert
        $response->assertStatus(200);
        $data = $response->json('data');
        $this->assertEquals('Z Group', $data[0]['name']);
        $this->assertEquals('A Group', $data[1]['name']);
    }

    public function test_index_with_pagination(): void
    {
        // Arrange
        ContractorGroups::factory()->count(5)->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        // Act
        $response = $this->getJson('/api/internal/contractor_groups?' . http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'page' => 1,
            'per_page' => 2
        ]));

        // Assert
        $response->assertStatus(200)
            ->assertJsonCount(2, 'data')
            ->assertJsonPath('meta.current_page', 1)
            ->assertJsonPath('meta.per_page', 2)
            ->assertJsonPath('meta.total', 5)
            ->assertJsonPath('meta.last_page', 3);
    }

    public function test_index_filter_name(): void
    {
        // Arrange
        ContractorGroups::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'name' => 'Specific Name'
        ]);

        ContractorGroups::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'name' => 'Other Name'
        ]);

        // Act
        $response = $this->getJson('/api/internal/contractor_groups?' . http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'filters' => [
                'name' => 'Specific Name'
            ]
        ]));

        // Assert
        $response->assertStatus(200)
            ->assertJsonCount(1, 'data')
            ->assertJsonPath('data.0.name', 'Specific Name');
    }

    public function test_show_nonexistent_contractor_group(): void
    {
        // Act
        $response = $this->getJson('/api/internal/contractor_groups/nonexistent-id');

        // Assert
        $response->assertStatus(404);
    }

    public function test_update_nonexistent_contractor_group(): void
    {
        // Act
        $response = $this->putJson('/api/internal/contractor_groups/nonexistent-id', [
            'name' => 'Updated Name'
        ]);

        // Assert
        $response->assertStatus(404);
    }

    public function test_delete_nonexistent_contractor_group(): void
    {
        // Act
        $response = $this->deleteJson('/api/internal/contractor_groups/nonexistent-id');

        // Assert
        $response->assertStatus(404);
    }

    public function test_create_contractor_group_with_long_name(): void
    {
        // Act
        $response = $this->postJson('/api/internal/contractor_groups', [
            'cabinet_id' => $this->cabinet->id,
            'name' => str_repeat('a', 256) // Превышает максимальную длину 255
        ]);

        // Assert
        $response->assertStatus(422)
            ->assertJsonValidationErrors(['name']);
    }
}
